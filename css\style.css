@font-face {
    font-family: '<PERSON><PERSON><PERSON>';
    src: url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');
}

/* ضبط الخصائص العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
}

/* تنسيق القائمة العلوية */
.navbar {
    font-weight: 500;
}

.navbar-brand {
    font-weight: 700;
}

.navbar .nav-link {
    color: #333;
    margin-right: 12px;
    font-size: 1.1rem;
}

.navbar .nav-link:hover {
    color: #0d6efd;
}

.navbar .nav-link.active {
    color: #0d6efd;
}

.buttons {
    margin-right: 20px;
}

.btn {
    margin-right: 8px;
    transition: all 0.3s;
}

.btn:hover {
    transform: translateY(-3px);
}

/* تنسيق السلايدر */
.carousel-item {
    overflow: hidden;
}

.carousel h2 {
    font-weight: 700;
}

/* تنسيق الأقسام المميزة */
.card {
    transition: all 0.3s;
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 20px;
}

/* تنسيق المميزات */
.feature {
    padding: 20px;
    transition: all 0.3s;
    border-radius: 8px;
}

.feature:hover {
    background-color: #f8f9fa;
    transform: translateY(-5px);
}

.feature-icon {
    color: #0d6efd;
}

/* تنسيق الفوتر */
footer {
    font-size: 0.95rem;
}

footer h5 {
    font-weight: 600;
}

footer a {
    transition: all 0.3s;
}

footer a:hover {
    opacity: 0.8;
    color: #0d6efd !important;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .navbar .nav-link {
        text-align: center;
        margin-right: 0;
    }
    
    .buttons {
        display: flex;
        justify-content: center;
        margin-right: 0;
        margin-top: 15px;
    }
    
    .feature {
        margin-bottom: 20px;
    }
} 