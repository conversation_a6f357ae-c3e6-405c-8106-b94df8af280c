<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سلة التسوق - Vela sweets</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- بداية الهيدر -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm">
            <div class="container">
                <a class="navbar-brand fw-bold fs-4" href="index.html">Vela sweets</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="index.html">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="products.html">المنتجات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="about.html">من نحن</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="contact.html">اتصل بنا</a>
                        </li>
                    </ul>
                    <div class="buttons ms-auto">
                        <a href="cart.html" class="btn">
                            <i class="fa fa-shopping-cart me-1"></i> سلة التسوق
                            <span class="badge bg-primary rounded-pill">0</span>
                        </a>

                    </div>
                </div>
            </div>
        </nav>
    </header>
    <!-- نهاية الهيدر -->

    <!-- بداية عنوان الصفحة -->
    <div class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1>سلة التسوق</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="index.html" class="text-decoration-none">الرئيسية</a></li>
                            <li class="breadcrumb-item active" aria-current="page">سلة التسوق</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- نهاية عنوان الصفحة -->

    <!-- بداية محتوى سلة التسوق -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- عناصر السلة -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-shopping-cart me-2"></i>عناصر السلة</h5>
                        </div>
                        <div class="card-body" id="cartItems">
                            <!-- سيتم عرض عناصر السلة هنا -->
                            <div class="text-center py-5" id="emptyCart">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h5>سلة التسوق فارغة</h5>
                                <p class="text-muted">ابدأ التسوق الآن واستمتع بأفضل الحلويات</p>
                                <a href="products.html" class="btn btn-dark">تصفح المنتجات</a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- ملخص الطلب -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-receipt me-2"></i>ملخص الطلب</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal">0 IQD</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>رسوم الشحن:</span>
                                <span id="shipping">0 IQD</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>المجموع الكلي:</strong>
                                <strong id="total">0 IQD</strong>
                            </div>
                            
                            <!-- كود الخصم -->
                            <div class="mb-3">
                                <label for="couponCode" class="form-label">كود الخصم</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="couponCode" placeholder="أدخل كود الخصم">
                                    <button class="btn btn-outline-secondary" type="button">تطبيق</button>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button class="btn btn-dark btn-lg" id="checkoutBtn" disabled>
                                    <i class="fas fa-credit-card me-2"></i>إتمام الطلب
                                </button>
                                <a href="products.html" class="btn btn-outline-dark">
                                    <i class="fas fa-arrow-left me-2"></i>متابعة التسوق
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="card mt-3">
                        <div class="card-body">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-2"></i>توصيل مجاني للطلبات أكثر من 50,000 IQD</li>
                                <li><i class="fas fa-check text-success me-2"></i>إمكانية الدفع عند التسليم</li>
                                <li><i class="fas fa-check text-success me-2"></i>ضمان الجودة والطزاجة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- نهاية محتوى سلة التسوق -->

    <!-- بداية الفوتر -->
    <footer class="bg-dark text-white pt-5 pb-4">
        <div class="container">
            <div class="row">
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">Vela sweets</h5>
                    <p>متجر متخصص في الحلويات الفاخرة بجميع أنواعها، نقدم منتجات عالية الجودة بأسعار مناسبة.</p>
                </div>
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="index.html" class="text-decoration-none text-white">الرئيسية</a></li>
                        <li class="mb-2"><a href="products.html" class="text-decoration-none text-white">المنتجات</a></li>
                        <li class="mb-2"><a href="about.html" class="text-decoration-none text-white">من نحن</a></li>
                        <li class="mb-2"><a href="contact.html" class="text-decoration-none text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">سياسات المتجر</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="privacy.html" class="text-decoration-none text-white">سياسة الخصوصية</a></li>
                        <li class="mb-2"><a href="terms.html" class="text-decoration-none text-white">الشروط والأحكام</a></li>
                        <li class="mb-2"><a href="shipping.html" class="text-decoration-none text-white">سياسة الشحن</a></li>
                        <li class="mb-2"><a href="return.html" class="text-decoration-none text-white">سياسة الاسترجاع</a></li>
                        <li class="mb-2"><a href="faq.html" class="text-decoration-none text-white">الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">تواصل معنا</h5>
                    <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i> البصرة، العراق</p>
                    <p class="mb-2"><i class="fas fa-phone me-2"></i> 07xxxxxxxxx</p>
                    <p class="mb-2"><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <div class="mt-4">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">جميع الحقوق محفوظة © 2025 Velasweets</p>
                </div>
            </div>
        </div>
    </footer>
    <!-- نهاية الفوتر -->

    <!-- سكريبت بوتستراب -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- سكريبت مخصص -->
    <script src="js/main.js"></script>
    
    <script>
        // تحميل وعرض عناصر السلة
        document.addEventListener('DOMContentLoaded', function() {
            loadCartItems();
            updateCartSummary();
        });

        function loadCartItems() {
            const cartData = localStorage.getItem('cart');
            const cartItemsContainer = document.getElementById('cartItems');
            const emptyCart = document.getElementById('emptyCart');
            
            if (!cartData || JSON.parse(cartData).items.length === 0) {
                emptyCart.style.display = 'block';
                return;
            }
            
            emptyCart.style.display = 'none';
            const cart = JSON.parse(cartData);
            
            let itemsHTML = '';
            cart.items.forEach(item => {
                itemsHTML += `
                    <div class="row align-items-center border-bottom py-3" data-item-id="${item.id}">
                        <div class="col-md-2">
                            <div class="bg-light d-flex align-items-center justify-content-center" style="height: 80px;">
                                <small class="text-muted">صورة المنتج</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>${item.name}</h6>
                            <small class="text-muted">${item.description || ''}</small>
                        </div>
                        <div class="col-md-2">
                            <span>${item.price.toLocaleString()} IQD</span>
                        </div>
                        <div class="col-md-2">
                            <div class="input-group input-group-sm">
                                <button class="btn btn-outline-secondary" onclick="updateQuantity('${item.id}', ${item.quantity - 1})">-</button>
                                <input type="text" class="form-control text-center" value="${item.quantity}" readonly>
                                <button class="btn btn-outline-secondary" onclick="updateQuantity('${item.id}', ${item.quantity + 1})">+</button>
                            </div>
                        </div>
                        <div class="col-md-2 text-end">
                            <div>
                                <strong>${(item.price * item.quantity).toLocaleString()} IQD</strong>
                            </div>
                            <button class="btn btn-sm btn-outline-danger mt-1" onclick="removeItem('${item.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            
            cartItemsContainer.innerHTML = itemsHTML;
        }

        function updateQuantity(itemId, newQuantity) {
            if (newQuantity < 1) {
                removeItem(itemId);
                return;
            }
            
            const cartData = localStorage.getItem('cart');
            if (!cartData) return;
            
            const cart = JSON.parse(cartData);
            const item = cart.items.find(item => item.id === itemId);
            if (item) {
                item.quantity = newQuantity;
                localStorage.setItem('cart', JSON.stringify(cart));
                loadCartItems();
                updateCartSummary();
            }
        }

        function removeItem(itemId) {
            const cartData = localStorage.getItem('cart');
            if (!cartData) return;
            
            const cart = JSON.parse(cartData);
            cart.items = cart.items.filter(item => item.id !== itemId);
            localStorage.setItem('cart', JSON.stringify(cart));
            loadCartItems();
            updateCartSummary();
        }

        function updateCartSummary() {
            const cartData = localStorage.getItem('cart');
            let subtotal = 0;
            let shipping = 3000; // رسوم الشحن الافتراضية للبصرة
            
            if (cartData) {
                const cart = JSON.parse(cartData);
                subtotal = cart.items.reduce((total, item) => total + (item.price * item.quantity), 0);
            }
            
            // شحن مجاني للطلبات أكثر من 50,000 IQD
            if (subtotal >= 50000) {
                shipping = 0;
            }
            
            const total = subtotal + shipping;
            
            document.getElementById('subtotal').textContent = subtotal.toLocaleString() + ' IQD';
            document.getElementById('shipping').textContent = shipping.toLocaleString() + ' IQD';
            document.getElementById('total').textContent = total.toLocaleString() + ' IQD';
            
            // تفعيل/تعطيل زر إتمام الطلب
            const checkoutBtn = document.getElementById('checkoutBtn');
            checkoutBtn.disabled = subtotal === 0;
        }
    </script>
</body>
</html>
