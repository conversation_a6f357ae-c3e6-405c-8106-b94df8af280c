# Vela sweets - متجر الحلويات الفاخرة

## نبذة عن المشروع

Vela sweets هو متجر إلكتروني متخصص في بيع الحلويات الفاخرة، يقع في البصرة - العراق. تم تصميمه باللغة العربية وبواجهة بسيطة ومتجاوبة مع جميع الأجهزة.

## المزايا الرئيسية

- تصميم متجاوب مع جميع الأجهزة (حاسوب، جوال، لوحي)
- دعم كامل للغة العربية مع اتجاه RTL
- هيكل منظم للمتجر مع الصفحات الأساسية
- تصميم بسيط وسهل الاستخدام
- نظام تسجيل دخول وإدارة المستخدمين
- نظام سلة التسوق
- نشرة بريدية تفاعلية
- صفحات سياسات شاملة

## الصفحات المتوفرة

### الصفحات الرئيسية
- الرئيسية: `index.html`
- المنتجات: `products.html` (جاهز لإضافة المنتجات)
- من نحن: `about.html`
- اتصل بنا: `contact.html`

### صفحات النظام
- تسجيل الدخول: `login.html`
- لوحة المستخدم: `dashboard.html`
- سلة التسوق: `cart.html`

### صفحات السياسات
- سياسة الخصوصية: `privacy.html`
- شروط الاستخدام: `terms.html`
- سياسة الشحن: `shipping.html`
- سياسة الإرجاع: `return.html`
- الأسئلة الشائعة: `faq.html`

## التقنيات المستخدمة

- HTML5
- CSS3
- Bootstrap 5 (RTL)
- JavaScript
- Font Awesome
- خط Tajawal العربي
- Local Storage للبيانات المؤقتة

## المواصفات التقنية

### العملة
- الدينار العراقي (IQD) فقط

### الموقع
- البصرة، العراق

### أرقام الهاتف
- تبدأ بـ 07 وتتكون من 11 رقم

### رسوم الشحن
- البصرة: 3,000 IQD (مجاني للطلبات أكثر من 50,000 IQD)
- باقي المحافظات: 5,000 IQD

### روابط التواصل الاجتماعي
- Facebook: # (جاهز للإضافة)
- Instagram: # (جاهز للإضافة)
- TikTok: # (جاهز للإضافة)

## الميزات المتقدمة

### نظام تسجيل الدخول
- تسجيل دخول آمن مع التحقق من البيانات
- لوحة مستخدم شخصية
- حفظ بيانات المستخدم محلياً

### النشرة البريدية
- نموذج اشتراك تفاعلي
- حفظ البيانات في Local Storage
- رسائل تأكيد وأخطاء

### سلة التسوق
- إضافة وحذف المنتجات
- تحديث الكميات
- حساب المجموع والشحن تلقائياً
- حفظ السلة محلياً

## كيفية البدء

1. قم بتنزيل أو استنساخ المشروع
2. قم بفتح ملف `index.html` في متصفحك
3. استعرض الصفحات المختلفة للمتجر

## بيانات تسجيل الدخول التجريبية

- **المدير:** <EMAIL> / admin123
- **مستخدم:** <EMAIL> / user123

## إضافة المنتجات

المتجر جاهز لإضافة المنتجات الحقيقية:

1. صفحة المنتجات فارغة ومهيأة
2. لا توجد فئات مؤقتة
3. نظام السلة جاهز للعمل
4. أسعار بالدينار العراقي

## التخصيص

يمكن تخصيص المتجر من خلال:

1. تعديل الألوان والخطوط في ملف `css/style.css`
2. إضافة الشعار والصور الحقيقية
3. تحديث معلومات التواصل
4. إضافة المنتجات الفعلية

## الملفات والمجلدات

```
VelaSweets/
├── index.html              # الصفحة الرئيسية
├── products.html           # صفحة المنتجات (فارغة)
├── about.html              # صفحة من نحن
├── contact.html            # صفحة اتصل بنا
├── login.html              # صفحة تسجيل الدخول
├── dashboard.html          # لوحة المستخدم
├── cart.html               # سلة التسوق
├── privacy.html            # سياسة الخصوصية
├── terms.html              # شروط الاستخدام
├── shipping.html           # سياسة الشحن
├── return.html             # سياسة الإرجاع
├── faq.html                # الأسئلة الشائعة
├── css/
│   └── style.css           # ملف التنسيقات
├── js/
│   └── main.js             # ملف JavaScript الرئيسي
├── data/
│   └── newsletter.json     # بيانات النشرة البريدية
└── README.md               # ملف التوثيق
```

## الحالة الحالية

✅ **مكتمل:**
- جميع الصفحات الأساسية
- نظام تسجيل الدخول
- سلة التسوق
- النشرة البريدية
- صفحات السياسات
- التصميم المتجاوب

🔄 **جاهز للإضافة:**
- المنتجات الحقيقية
- الصور والشعارات
- روابط التواصل الاجتماعي
- أرقام الهاتف الفعلية

## المطورون

تم تطوير هذا المشروع بواسطة فريق Vela sweets.

## الترخيص

Velasweets - جميع الحقوق محفوظة © 2025