# Vela sweets - متجر الحلويات الفاخرة

## نبذة عن المشروع

Vela sweets هو متجر إلكتروني متخصص في بيع الحلويات الفاخرة، يقع في البصرة - العراق. تم تصميمه باللغة العربية وبواجهة بسيطة ومتجاوبة مع جميع الأجهزة.

## المزايا الرئيسية

- تصميم متجاوب مع جميع الأجهزة (حاسوب، جوال، لوحي)
- دعم كامل للغة العربية مع اتجاه RTL
- هيكل منظم للمتجر مع الصفحات الأساسية
- تصميم بسيط وسهل الاستخدام
- نظام تسجيل دخول وإدارة المستخدمين
- نظام سلة التسوق
- نشرة بريدية تفاعلية
- صفحات سياسات شاملة

## الصفحات المتوفرة

### الصفحات الرئيسية
- الرئيسية: `index.html`
- المنتجات: `products.html` (جاهز لإضافة المنتجات)
- من نحن: `about.html`
- اتصل بنا: `contact.html`

### صفحات النظام
- تسجيل الدخول: `login.html`
- لوحة المستخدم: `dashboard.html`
- سلة التسوق: `cart.html`

### صفحات السياسات
- سياسة الخصوصية: `privacy.html`
- شروط الاستخدام: `terms.html`
- سياسة الشحن: `shipping.html`
- سياسة الإرجاع: `return.html`
- الأسئلة الشائعة: `faq.html`

## التقنيات المستخدمة

- HTML5
- CSS3
- Bootstrap 5 (RTL)
- JavaScript
- Font Awesome
- خط Tajawal العربي
- Local Storage للبيانات المؤقتة

## المواصفات التقنية

### العملة
- الدينار العراقي (IQD) فقط

### الموقع
- البصرة، العراق

### أرقام الهاتف
- تبدأ بـ 07 وتتكون من 11 رقم

### رسوم الشحن
- البصرة: 3,000 IQD (مجاني للطلبات أكثر من 50,000 IQD)
- باقي المحافظات: 5,000 IQD

### روابط التواصل الاجتماعي
- Facebook: # (جاهز للإضافة)
- Instagram: # (جاهز للإضافة)
- TikTok: # (جاهز للإضافة)

## الميزات المتقدمة

### نظام تسجيل الدخول
- تسجيل دخول آمن مع التحقق من البيانات
- لوحة مستخدم شخصية
- حفظ بيانات المستخدم محلياً

### النشرة البريدية
- نموذج اشتراك تفاعلي
- حفظ البيانات في Local Storage
- رسائل تأكيد وأخطاء

### سلة التسوق
- إضافة وحذف المنتجات
- تحديث الكميات
- حساب المجموع والشحن تلقائياً
- حفظ السلة محلياً

## كيفية البدء

1. قم بتنزيل أو استنساخ المشروع
2. قم بفتح ملف `index.html` في متصفحك
3. استعرض الصفحات المختلفة للمتجر

## بيانات تسجيل الدخول التجريبية

- **المدير:** <EMAIL> / admin123
- **مستخدم:** <EMAIL> / user123

## إضافة المنتجات

المتجر جاهز لإضافة المنتجات الحقيقية:

1. صفحة المنتجات فارغة ومهيأة
2. لا توجد فئات مؤقتة
3. نظام السلة جاهز للعمل
4. أسعار بالدينار العراقي

## التخصيص

يمكن تخصيص المتجر من خلال:

1. تعديل الألوان والخطوط في ملف `css/style.css`
2. إضافة الشعار والصور الحقيقية
3. تحديث معلومات التواصل
4. إضافة المنتجات الفعلية

## الملفات والمجلدات

```
VelaSweets/
├── index.html              # الصفحة الرئيسية
├── products.html           # صفحة المنتجات (فارغة)
├── about.html              # صفحة من نحن
├── contact.html            # صفحة اتصل بنا
├── login.html              # صفحة تسجيل الدخول
├── dashboard.html          # لوحة المستخدم
├── cart.html               # سلة التسوق
├── privacy.html            # سياسة الخصوصية
├── terms.html              # شروط الاستخدام
├── shipping.html           # سياسة الشحن
├── return.html             # سياسة الإرجاع
├── faq.html                # الأسئلة الشائعة
├── css/
│   └── style.css           # ملف التنسيقات
├── js/
│   └── main.js             # ملف JavaScript الرئيسي
├── data/
│   └── newsletter.json     # بيانات النشرة البريدية
└── README.md               # ملف التوثيق
```

## نظام الاختبار الذكي 🧪

### المزايا المتقدمة:
- **واجهة عصرية ومتجاوبة** مع تصميم متدرج وتأثيرات بصرية
- **تشخيص ذكي** لكل اختبار مع شرح المشكلة والحل المقترح
- **تقسيم منطقي** للاختبارات حسب الأنظمة (مصادقة، عملات، لغات، منتجات، سلة، واجهة)
- **ألوان دالة على الحالة**: ✅ نجاح (أخضر) | ❌ فشل (أحمر) | ⚠️ تحذير (أصفر) | ⏳ قيد الفحص (رمادي متحرك)
- **تقرير أخطاء شامل** قابل للنسخ والتحميل
- **إحصائيات مباشرة** وشريط تقدم تفاعلي

### الاختبارات المشمولة:

#### 🛡️ نظام المصادقة والأمان:
- فحص صفحة تسجيل الدخول ووظائفها
- التحقق من صحة البريد الإلكتروني
- التحقق من أرقام الهاتف العراقية (07xxxxxxxxx)
- فحص نظام كلمات المرور

#### 💰 نظام العملات والأسعار:
- عرض الأسعار بالدينار العراقي فقط
- رسوم الشحن للبصرة (3000 IQD)
- رسوم الشحن للمحافظات الأخرى (5000 IQD)
- الشحن المجاني للطلبات أكثر من 50,000 IQD

#### 🌐 نظام اللغات المتعددة:
- دعم اللغة العربية مع اتجاه RTL
- دعم اللغة الكردية
- دعم اللغة الإنجليزية مع اتجاه LTR
- حفظ اللغة المختارة في localStorage

#### 📦 نظام المنتجات:
- عدم وجود منتجات وهمية أو تجريبية
- هيكل بيانات المنتجات الصحيح
- عدم وجود فئات مؤقتة
- صفحة المنتجات فارغة وجاهزة

#### 🛒 سلة التسوق والطلبات:
- إضافة المنتجات للسلة
- تعديل الكميات
- حذف المنتجات
- حفظ السلة في localStorage
- حساب المجموع الصحيح

#### 🖥️ واجهة المستخدم:
- فحص الروابط والأزرار
- التوافق مع الهاتف والتابلت
- تغيير الواجهة بعد تسجيل الدخول
- عمل نموذج النشرة البريدية

### كيفية الاستخدام:
1. افتح `test.html` في المتصفح
2. اضغط على "بدء الفحص الشامل"
3. راقب النتائج المباشرة مع التشخيص الذكي
4. انسخ تقرير الأخطاء للمراجعة أو المشاركة

## الحالة الحالية

✅ **مكتمل:**
- جميع الصفحات الأساسية
- نظام تسجيل الدخول
- سلة التسوق
- النشرة البريدية
- صفحات السياسات
- التصميم المتجاوب
- **نظام اختبار ذكي شامل**

🔄 **جاهز للإضافة:**
- المنتجات الحقيقية
- الصور والشعارات
- روابط التواصل الاجتماعي
- أرقام الهاتف الفعلية

## المطورون

تم تطوير هذا المشروع بواسطة فريق Vela sweets.

## الترخيص

جميع الحقوق محفوظة © 2025 Velasweets