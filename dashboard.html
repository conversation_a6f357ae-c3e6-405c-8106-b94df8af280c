<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة المستخدم - Vela sweets</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- بداية الهيدر -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm">
            <div class="container">
                <a class="navbar-brand fw-bold fs-4" href="index.html">Vela sweets</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="index.html">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="products.html">المنتجات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="about.html">من نحن</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="contact.html">اتصل بنا</a>
                        </li>
                    </ul>
                    <div class="buttons ms-auto">
                        <span class="me-3" id="userWelcome">مرحباً</span>
                        <button class="btn btn-outline-danger" onclick="logout()">تسجيل الخروج</button>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <!-- نهاية الهيدر -->

    <!-- بداية عنوان الصفحة -->
    <div class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1>لوحة المستخدم</h1>
                    <p class="lead">مرحباً بك في حسابك الشخصي</p>
                </div>
            </div>
        </div>
    </div>
    <!-- نهاية عنوان الصفحة -->

    <!-- بداية محتوى لوحة المستخدم -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- القائمة الجانبية -->
                <div class="col-md-3 mb-4">
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h6 class="mb-0">القائمة الرئيسية</h6>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="#profile" class="list-group-item list-group-item-action active" onclick="showSection('profile')">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a>
                            <a href="#orders" class="list-group-item list-group-item-action" onclick="showSection('orders')">
                                <i class="fas fa-shopping-bag me-2"></i>طلباتي
                            </a>
                            <a href="#addresses" class="list-group-item list-group-item-action" onclick="showSection('addresses')">
                                <i class="fas fa-map-marker-alt me-2"></i>العناوين
                            </a>
                            <a href="#settings" class="list-group-item list-group-item-action" onclick="showSection('settings')">
                                <i class="fas fa-cog me-2"></i>الإعدادات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="col-md-9">
                    <!-- قسم الملف الشخصي -->
                    <div id="profile-section" class="dashboard-section">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-user me-2"></i>الملف الشخصي</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>معلومات الحساب</h6>
                                        <p><strong>البريد الإلكتروني:</strong> <span id="userEmail"><EMAIL></span></p>
                                        <p><strong>تاريخ التسجيل:</strong> 1 يناير 2025</p>
                                        <p><strong>حالة الحساب:</strong> <span class="badge bg-success">نشط</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>إحصائيات سريعة</h6>
                                        <p><strong>إجمالي الطلبات:</strong> 0</p>
                                        <p><strong>المبلغ المنفق:</strong> 0 IQD</p>
                                        <p><strong>النقاط المكتسبة:</strong> 0</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم الطلبات -->
                    <div id="orders-section" class="dashboard-section" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-shopping-bag me-2"></i>طلباتي</h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center py-5">
                                    <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                                    <h5>لا توجد طلبات حتى الآن</h5>
                                    <p class="text-muted">ابدأ التسوق الآن واستمتع بأفضل الحلويات</p>
                                    <a href="products.html" class="btn btn-dark">تصفح المنتجات</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم العناوين -->
                    <div id="addresses-section" class="dashboard-section" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-map-marker-alt me-2"></i>عناوين التوصيل</h5>
                                <button class="btn btn-sm btn-dark">إضافة عنوان جديد</button>
                            </div>
                            <div class="card-body">
                                <div class="text-center py-5">
                                    <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                                    <h5>لا توجد عناوين محفوظة</h5>
                                    <p class="text-muted">أضف عنوان التوصيل لتسهيل عملية الطلب</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم الإعدادات -->
                    <div id="settings-section" class="dashboard-section" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cog me-2"></i>إعدادات الحساب</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>الإشعارات</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                            <label class="form-check-label" for="emailNotifications">
                                                إشعارات البريد الإلكتروني
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="smsNotifications">
                                            <label class="form-check-label" for="smsNotifications">
                                                إشعارات الرسائل النصية
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>الخصوصية</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="profilePublic">
                                            <label class="form-check-label" for="profilePublic">
                                                ملف شخصي عام
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="shareData">
                                            <label class="form-check-label" for="shareData">
                                                مشاركة البيانات للتحسين
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-dark">حفظ التغييرات</button>
                                    <button class="btn btn-outline-danger">تغيير كلمة المرور</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- نهاية محتوى لوحة المستخدم -->

    <!-- بداية الفوتر -->
    <footer class="bg-dark text-white pt-5 pb-4">
        <div class="container">
            <div class="row">
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">Vela sweets</h5>
                    <p>متجر متخصص في الحلويات الفاخرة بجميع أنواعها، نقدم منتجات عالية الجودة بأسعار مناسبة.</p>
                </div>
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="index.html" class="text-decoration-none text-white">الرئيسية</a></li>
                        <li class="mb-2"><a href="products.html" class="text-decoration-none text-white">المنتجات</a></li>
                        <li class="mb-2"><a href="about.html" class="text-decoration-none text-white">من نحن</a></li>
                        <li class="mb-2"><a href="contact.html" class="text-decoration-none text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">سياسات المتجر</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="privacy.html" class="text-decoration-none text-white">سياسة الخصوصية</a></li>
                        <li class="mb-2"><a href="terms.html" class="text-decoration-none text-white">الشروط والأحكام</a></li>
                        <li class="mb-2"><a href="shipping.html" class="text-decoration-none text-white">سياسة الشحن</a></li>
                        <li class="mb-2"><a href="return.html" class="text-decoration-none text-white">سياسة الاسترجاع</a></li>
                        <li class="mb-2"><a href="faq.html" class="text-decoration-none text-white">الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">تواصل معنا</h5>
                    <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i> البصرة، العراق</p>
                    <p class="mb-2"><i class="fas fa-phone me-2"></i> 07xxxxxxxxx</p>
                    <p class="mb-2"><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <div class="mt-4">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">Velasweets © 2025 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>
    <!-- نهاية الفوتر -->

    <!-- سكريبت بوتستراب -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- سكريبت مخصص -->
    <script src="js/main.js"></script>
    
    <script>
        // التحقق من تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            const isLoggedIn = localStorage.getItem('userLoggedIn');
            const userEmail = localStorage.getItem('userEmail');
            
            if (!isLoggedIn) {
                window.location.href = 'login.html';
                return;
            }
            
            // عرض بيانات المستخدم
            if (userEmail) {
                document.getElementById('userWelcome').textContent = `مرحباً ${userEmail}`;
                document.getElementById('userEmail').textContent = userEmail;
            }
        });

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('userLoggedIn');
            localStorage.removeItem('userEmail');
            window.location.href = 'index.html';
        }

        // إظهار الأقسام المختلفة
        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('.dashboard-section');
            sections.forEach(section => section.style.display = 'none');
            
            // إظهار القسم المحدد
            document.getElementById(sectionName + '-section').style.display = 'block';
            
            // تحديث القائمة النشطة
            const menuItems = document.querySelectorAll('.list-group-item');
            menuItems.forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
