// التأكد من تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل المتجر بنجاح');
    
    // تفعيل مكونات بوتستراب
    activateBootstrapComponents();
    
    // إضافة استجابة لأحداث الأزرار
    setupButtonEvents();
});

// تفعيل مكونات بوتستراب
function activateBootstrapComponents() {
    // تفعيل التلميحات
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل التحقق من الفورم
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
}

// إعداد أحداث الأزرار
function setupButtonEvents() {
    // زر الاشتراك في النشرة البريدية
    var newsletterForm = document.getElementById('newsletterForm');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleNewsletterSubscription();
        });
    }
}

// معالجة الاشتراك في النشرة البريدية
function handleNewsletterSubscription() {
    const emailInput = document.getElementById('newsletterEmail');
    const messageDiv = document.getElementById('newsletterMessage');
    const email = emailInput.value.trim();

    // التحقق من صحة البريد الإلكتروني
    if (!email) {
        showNewsletterMessage('يرجى إدخال البريد الإلكتروني', 'error');
        return;
    }

    if (!validateEmail(email)) {
        showNewsletterMessage('يرجى إدخال بريد إلكتروني صحيح', 'error');
        return;
    }

    // إظهار رسالة التحميل
    showNewsletterMessage('جاري الاشتراك...', 'info');

    // محاكاة عملية الاشتراك
    setTimeout(() => {
        // حفظ البريد الإلكتروني في التخزين المحلي
        saveNewsletterSubscription(email);

        // إظهار رسالة النجاح
        showNewsletterMessage('تم الاشتراك بنجاح! شكراً لانضمامك إلى نشرتنا البريدية', 'success');

        // مسح الحقل
        emailInput.value = '';
    }, 1500);
}

// إظهار رسائل النشرة البريدية
function showNewsletterMessage(message, type) {
    const messageDiv = document.getElementById('newsletterMessage');
    if (messageDiv) {
        messageDiv.style.display = 'block';
        messageDiv.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} mt-3`;
        messageDiv.textContent = message;

        // إخفاء الرسالة بعد 5 ثوان للرسائل الناجحة
        if (type === 'success') {
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }
    }
}

// حفظ اشتراك النشرة البريدية
function saveNewsletterSubscription(email) {
    // الحصول على الاشتراكات الحالية
    let subscriptions = JSON.parse(localStorage.getItem('newsletterSubscriptions') || '[]');

    // التحقق من عدم وجود البريد مسبقاً
    if (!subscriptions.includes(email)) {
        subscriptions.push(email);
        localStorage.setItem('newsletterSubscriptions', JSON.stringify(subscriptions));

        // حفظ في ملف JSON (محاكاة)
        console.log('تم حفظ الاشتراك:', email);
        console.log('جميع الاشتراكات:', subscriptions);
    }
}

// دالة للتحقق من صحة الإيميل
function validateEmail(email) {
    var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

// دالة للتمرير السلس إلى عنصر
function scrollToElement(elementId) {
    var element = document.getElementById(elementId);
    if (element) {
        window.scrollTo({
            top: element.offsetTop - 70,
            behavior: 'smooth'
        });
    }
}

// إعداد زر العودة للأعلى
window.onscroll = function() {
    var backToTopBtn = document.getElementById("backToTop");
    if (backToTopBtn) {
        if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
            backToTopBtn.style.display = "block";
        } else {
            backToTopBtn.style.display = "none";
        }
    }
};

// للإضافة المستقبلية: سلة التسوق
class ShoppingCart {
    constructor() {
        this.items = [];
        this.total = 0;
    }
    
    addItem(product) {
        let found = false;
        for (let i = 0; i < this.items.length; i++) {
            if (this.items[i].id === product.id) {
                this.items[i].quantity++;
                found = true;
                break;
            }
        }
        
        if (!found) {
            product.quantity = 1;
            this.items.push(product);
        }
        
        this.updateTotal();
        this.saveToLocalStorage();
        this.updateCartBadge();
    }
    
    removeItem(productId) {
        this.items = this.items.filter(item => item.id !== productId);
        this.updateTotal();
        this.saveToLocalStorage();
        this.updateCartBadge();
    }
    
    updateQuantity(productId, quantity) {
        for (let i = 0; i < this.items.length; i++) {
            if (this.items[i].id === productId) {
                this.items[i].quantity = quantity;
                break;
            }
        }
        
        this.updateTotal();
        this.saveToLocalStorage();
    }
    
    updateTotal() {
        this.total = 0;
        for (let i = 0; i < this.items.length; i++) {
            this.total += this.items[i].price * this.items[i].quantity;
        }
    }
    
    clear() {
        this.items = [];
        this.total = 0;
        this.saveToLocalStorage();
        this.updateCartBadge();
    }
    
    getItemCount() {
        let count = 0;
        for (let i = 0; i < this.items.length; i++) {
            count += this.items[i].quantity;
        }
        return count;
    }
    
    updateCartBadge() {
        const badge = document.querySelector('.badge');
        if (badge) {
            badge.textContent = this.getItemCount();
        }
    }
    
    saveToLocalStorage() {
        localStorage.setItem('cart', JSON.stringify({
            items: this.items,
            total: this.total
        }));
    }
    
    loadFromLocalStorage() {
        const cartData = localStorage.getItem('cart');
        if (cartData) {
            const cart = JSON.parse(cartData);
            this.items = cart.items;
            this.total = cart.total;
            this.updateCartBadge();
        }
    }
}

// إنشاء كائن سلة التسوق
const cart = new ShoppingCart();

// تحميل السلة من التخزين المحلي
document.addEventListener('DOMContentLoaded', function() {
    cart.loadFromLocalStorage();
}); 