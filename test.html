<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 نظام اختبار ذكي - Vela sweets</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --success-color: #198754;
            --error-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #0dcaf0;
            --testing-color: #6c757d;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .test-category {
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .test-category:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .category-header {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .category-content {
            padding: 20px;
            display: none;
        }

        .category-content.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .test-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .test-item.success {
            border-left-color: var(--success-color);
            background: #f8fff9;
        }

        .test-item.error {
            border-left-color: var(--error-color);
            background: #fff5f5;
        }

        .test-item.warning {
            border-left-color: var(--warning-color);
            background: #fffdf5;
        }

        .test-item.testing {
            border-left-color: var(--testing-color);
            background: #f8f9fa;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .test-status {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .test-description {
            color: #6c757d;
            margin-bottom: 10px;
        }

        .test-solution {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
            font-size: 0.9em;
        }

        .progress-bar-container {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--success-color), #20c997);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .control-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .btn-smart {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-smart:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
            color: white;
        }

        .btn-copy {
            background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-copy:hover {
            transform: translateY(-1px);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-report {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            line-height: 1.6;
        }

        .category-icon {
            font-size: 1.5em;
            margin-left: 10px;
            vertical-align: middle;
        }

        .test-details {
            font-size: 0.85em;
            color: #495057;
            margin-top: 8px;
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
                border-radius: 15px 15px 0 0;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-flask"></i> نظام الاختبار الذكي</h1>
            <h2>Vela sweets - Smart Testing System</h2>
            <p class="lead mb-0">نظام تشخيص وفحص شامل لجميع وظائف المتجر</p>
        </div>

        <!-- Control Panel -->
        <div class="container-fluid p-4">
            <div class="control-panel">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h4><i class="fas fa-cogs me-2"></i>لوحة التحكم</h4>
                        <p class="text-muted mb-0">ابدأ الفحص الشامل لجميع أنظمة المتجر</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-smart me-3" onclick="startComprehensiveTest()">
                            <i class="fas fa-play me-2"></i>بدء الفحص الشامل
                            <span id="loadingSpinner" class="loading-spinner" style="display: none;"></span>
                        </button>
                        <button class="btn btn-copy" onclick="copyErrorReport()">
                            <i class="fas fa-copy me-2"></i>نسخ تقرير الأخطاء
                        </button>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-bar-container">
                    <div class="progress-bar" id="progressBar"></div>
                </div>

                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number text-success" id="passedTests">0</div>
                        <div class="stat-label">اختبارات ناجحة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-danger" id="failedTests">0</div>
                        <div class="stat-label">اختبارات فاشلة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-warning" id="warningTests">0</div>
                        <div class="stat-label">تحذيرات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-info" id="totalTests">0</div>
                        <div class="stat-label">إجمالي الاختبارات</div>
                    </div>
                </div>
            </div>

            <!-- Authentication System Tests -->
            <div class="test-category" id="authCategory">
                <div class="category-header" onclick="toggleCategory('auth')">
                    <h4><i class="fas fa-shield-alt category-icon"></i>نظام المصادقة والأمان</h4>
                    <small class="text-muted">فحص تسجيل الدخول، إنشاء الحسابات، والتحقق من البيانات</small>
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </div>
                <div class="category-content" id="authContent">
                    <div id="authTests"></div>
                </div>
            </div>

            <!-- Currency System Tests -->
            <div class="test-category" id="currencyCategory">
                <div class="category-header" onclick="toggleCategory('currency')">
                    <h4><i class="fas fa-coins category-icon"></i>نظام العملات والأسعار</h4>
                    <small class="text-muted">فحص عرض الأسعار بالدينار العراقي ورسوم الشحن</small>
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </div>
                <div class="category-content" id="currencyContent">
                    <div id="currencyTests"></div>
                </div>
            </div>

            <!-- Language System Tests -->
            <div class="test-category" id="languageCategory">
                <div class="category-header" onclick="toggleCategory('language')">
                    <h4><i class="fas fa-language category-icon"></i>نظام اللغات المتعددة</h4>
                    <small class="text-muted">فحص دعم العربية والكردية والإنجليزية</small>
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </div>
                <div class="category-content" id="languageContent">
                    <div id="languageTests"></div>
                </div>
            </div>

            <!-- Products System Tests -->
            <div class="test-category" id="productsCategory">
                <div class="category-header" onclick="toggleCategory('products')">
                    <h4><i class="fas fa-box category-icon"></i>نظام المنتجات</h4>
                    <small class="text-muted">فحص المنتجات الحقيقية وعدم وجود بيانات وهمية</small>
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </div>
                <div class="category-content" id="productsContent">
                    <div id="productsTests"></div>
                </div>
            </div>

            <!-- Cart System Tests -->
            <div class="test-category" id="cartCategory">
                <div class="category-header" onclick="toggleCategory('cart')">
                    <h4><i class="fas fa-shopping-cart category-icon"></i>سلة التسوق والطلبات</h4>
                    <small class="text-muted">فحص إضافة المنتجات، تعديل الكميات، وحفظ الطلبات</small>
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </div>
                <div class="category-content" id="cartContent">
                    <div id="cartTests"></div>
                </div>
            </div>

            <!-- UI System Tests -->
            <div class="test-category" id="uiCategory">
                <div class="category-header" onclick="toggleCategory('ui')">
                    <h4><i class="fas fa-desktop category-icon"></i>واجهة المستخدم</h4>
                    <small class="text-muted">فحص الروابط، الأزرار، والتوافق مع الأجهزة المختلفة</small>
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </div>
                <div class="category-content" id="uiContent">
                    <div id="uiTests"></div>
                </div>
            </div>

            <!-- Error Report Section -->
            <div class="control-panel" id="errorReportSection" style="display: none;">
                <h4><i class="fas fa-bug me-2"></i>تقرير الأخطاء والحلول</h4>
                <p class="text-muted">تقرير شامل بجميع الأخطاء المكتشفة مع الحلول المقترحة</p>
                <div class="error-report" id="errorReport"></div>
                <div class="mt-3">
                    <button class="btn btn-copy me-2" onclick="copyErrorReport()">
                        <i class="fas fa-copy me-2"></i>نسخ التقرير
                    </button>
                    <button class="btn btn-outline-secondary" onclick="downloadReport()">
                        <i class="fas fa-download me-2"></i>تحميل التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

        <!-- اختبار الوظائف -->
        <div class="test-section test-warning">
            <h3><i class="fas fa-cogs me-2"></i>اختبار الوظائف التفاعلية</h3>
            
            <div class="mb-4">
                <h5>1. النشرة البريدية:</h5>
                <form id="testNewsletter" class="row g-3">
                    <div class="col-auto">
                        <input type="email" class="form-control" id="testEmail" placeholder="<EMAIL>" required>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary">اختبار الاشتراك</button>
                    </div>
                </form>
                <div id="newsletterResult" class="mt-2"></div>
            </div>

            <div class="mb-4">
                <h5>2. تسجيل الدخول:</h5>
                <p><strong>بيانات تجريبية:</strong></p>
                <ul>
                    <li><EMAIL> / admin123</li>
                    <li><EMAIL> / user123</li>
                </ul>
                <a href="login.html" class="btn btn-outline-primary">اختبار تسجيل الدخول</a>
            </div>

            <div class="mb-4">
                <h5>3. سلة التسوق:</h5>
                <button class="btn btn-outline-success" onclick="testCart()">إضافة منتج تجريبي للسلة</button>
                <a href="cart.html" class="btn btn-outline-primary ms-2">عرض السلة</a>
                <div id="cartResult" class="mt-2"></div>
            </div>
        </div>

        <!-- اختبار التنظيف -->
        <div class="test-section test-success">
            <h3><i class="fas fa-broom me-2"></i>التنظيف المكتمل</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>تم حذف:</h5>
                    <ul>
                        <li>قسم "الأقسام المميزة" ✅</li>
                        <li>قسم "فريق العمل" ✅</li>
                        <li>جميع المنتجات الوهمية ✅</li>
                        <li>جميع الفئات المؤقتة ✅</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>تم تحديث:</h5>
                    <ul>
                        <li>نص الحقوق إلى 2025 ✅</li>
                        <li>اسم المتجر إلى "Vela sweets" ✅</li>
                        <li>الموقع إلى البصرة، العراق ✅</li>
                        <li>العملة إلى IQD فقط ✅</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- ملخص الحالة -->
        <div class="test-section test-success">
            <h3><i class="fas fa-check-circle me-2"></i>ملخص الحالة النهائية</h3>
            <div class="alert alert-success">
                <h4>🎉 المتجر جاهز للانطلاق!</h4>
                <p><strong>تم إنجاز جميع المتطلبات بنجاح:</strong></p>
                <ul class="mb-0">
                    <li>✅ حذف قسم "الأقسام المميزة" بالكامل</li>
                    <li>✅ تحديد العملة الوحيدة: الدينار العراقي (IQD)</li>
                    <li>✅ تحديث نص الحقوق: "جميع الحقوق محفوظة © 2025 Velasweets"</li>
                    <li>✅ تثبيت اسم المتجر: "Vela sweets" في جميع الواجهات</li>
                    <li>✅ حذف قسم "فريق العمل" بالكامل</li>
                    <li>✅ تحديد الموقع: العراق - البصرة</li>
                    <li>✅ تحديث روابط التواصل الاجتماعي (Facebook, Instagram, TikTok)</li>
                    <li>✅ تنظيف قسم المنتجات من المحتوى الوهمي</li>
                    <li>✅ حذف جميع الفئات الوهمية</li>
                    <li>✅ إنشاء جميع الصفحات المطلوبة</li>
                    <li>✅ تفعيل صفحة تسجيل الدخول مع الوظائف الكاملة</li>
                    <li>✅ تفعيل النشرة البريدية مع حفظ البيانات</li>
                    <li>✅ فحص وإصلاح جميع الأزرار والروابط</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables for testing system
        let testResults = {
            passed: 0,
            failed: 0,
            warnings: 0,
            total: 0,
            errors: []
        };

        let currentTestIndex = 0;
        let totalTests = 0;

        // Test definitions
        const testSuites = {
            auth: {
                name: 'نظام المصادقة',
                tests: [
                    {
                        name: 'فحص صفحة تسجيل الدخول',
                        description: 'التحقق من وجود صفحة تسجيل الدخول ووظائفها',
                        test: testLoginPage
                    },
                    {
                        name: 'فحص التحقق من البريد الإلكتروني',
                        description: 'التأكد من صحة التحقق من البريد الإلكتروني',
                        test: testEmailValidation
                    },
                    {
                        name: 'فحص التحقق من رقم الهاتف العراقي',
                        description: 'التأكد من قبول أرقام الهاتف العراقية فقط (07xxxxxxxxx)',
                        test: testIraqiPhoneValidation
                    },
                    {
                        name: 'فحص نظام كلمات المرور',
                        description: 'التحقق من قوة كلمات المرور والتشفير',
                        test: testPasswordSystem
                    }
                ]
            },
            currency: {
                name: 'نظام العملات',
                tests: [
                    {
                        name: 'فحص عرض الأسعار بالدينار العراقي',
                        description: 'التأكد من عرض جميع الأسعار بـ IQD فقط',
                        test: testCurrencyDisplay
                    },
                    {
                        name: 'فحص رسوم الشحن للبصرة',
                        description: 'التحقق من رسوم الشحن 3000 IQD للبصرة',
                        test: testBasraShipping
                    },
                    {
                        name: 'فحص رسوم الشحن للمحافظات الأخرى',
                        description: 'التحقق من رسوم الشحن 5000 IQD للمحافظات الأخرى',
                        test: testOtherProvincesShipping
                    },
                    {
                        name: 'فحص الشحن المجاني',
                        description: 'التحقق من الشحن المجاني للطلبات أكثر من 50,000 IQD',
                        test: testFreeShipping
                    }
                ]
            },
            language: {
                name: 'نظام اللغات',
                tests: [
                    {
                        name: 'فحص دعم اللغة العربية',
                        description: 'التحقق من دعم اللغة العربية واتجاه RTL',
                        test: testArabicLanguage
                    },
                    {
                        name: 'فحص دعم اللغة الكردية',
                        description: 'التحقق من دعم اللغة الكردية',
                        test: testKurdishLanguage
                    },
                    {
                        name: 'فحص دعم اللغة الإنجليزية',
                        description: 'التحقق من دعم اللغة الإنجليزية واتجاه LTR',
                        test: testEnglishLanguage
                    },
                    {
                        name: 'فحص حفظ اللغة المختارة',
                        description: 'التأكد من حفظ اللغة في localStorage',
                        test: testLanguageStorage
                    }
                ]
            },
            products: {
                name: 'نظام المنتجات',
                tests: [
                    {
                        name: 'فحص عدم وجود منتجات وهمية',
                        description: 'التأكد من عدم وجود منتجات تجريبية أو وهمية',
                        test: testNoFakeProducts
                    },
                    {
                        name: 'فحص هيكل بيانات المنتجات',
                        description: 'التحقق من وجود الحقول المطلوبة (اسم، سعر، فئة، صورة)',
                        test: testProductStructure
                    },
                    {
                        name: 'فحص فئات المنتجات',
                        description: 'التأكد من عدم وجود فئات مؤقتة',
                        test: testProductCategories
                    },
                    {
                        name: 'فحص صفحة المنتجات',
                        description: 'التحقق من أن صفحة المنتجات فارغة وجاهزة',
                        test: testProductsPage
                    }
                ]
            },
            cart: {
                name: 'سلة التسوق',
                tests: [
                    {
                        name: 'فحص إضافة منتج للسلة',
                        description: 'التحقق من إمكانية إضافة منتجات للسلة',
                        test: testAddToCart
                    },
                    {
                        name: 'فحص تعديل الكمية',
                        description: 'التحقق من إمكانية تعديل كمية المنتجات',
                        test: testUpdateQuantity
                    },
                    {
                        name: 'فحص حذف منتج من السلة',
                        description: 'التحقق من إمكانية حذف المنتجات',
                        test: testRemoveFromCart
                    },
                    {
                        name: 'فحص حفظ السلة',
                        description: 'التأكد من حفظ السلة في localStorage',
                        test: testCartStorage
                    },
                    {
                        name: 'فحص حساب المجموع',
                        description: 'التحقق من صحة حساب مجموع السلة',
                        test: testCartTotal
                    }
                ]
            },
            ui: {
                name: 'واجهة المستخدم',
                tests: [
                    {
                        name: 'فحص الروابط والأزرار',
                        description: 'التحقق من عمل جميع الروابط والأزرار',
                        test: testLinksAndButtons
                    },
                    {
                        name: 'فحص التوافق مع الهاتف',
                        description: 'التحقق من التصميم المتجاوب للهواتف',
                        test: testMobileCompatibility
                    },
                    {
                        name: 'فحص حالة تسجيل الدخول',
                        description: 'التحقق من تغيير الواجهة بعد تسجيل الدخول',
                        test: testLoginState
                    },
                    {
                        name: 'فحص النشرة البريدية',
                        description: 'التحقق من عمل نموذج النشرة البريدية',
                        test: testNewsletterForm
                    }
                ]
            }
        };

        // Calculate total tests
        function calculateTotalTests() {
            let total = 0;
            Object.values(testSuites).forEach(suite => {
                total += suite.tests.length;
            });
            return total;
        }

        // Toggle category visibility
        function toggleCategory(categoryName) {
            const content = document.getElementById(categoryName + 'Content');
            const icon = document.querySelector(`#${categoryName}Category .fa-chevron-down`);

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('show');
                icon.style.transform = 'rotate(180deg)';
            }
        }

        // Create test item HTML
        function createTestItem(test, status = 'pending', result = null) {
            const statusClass = status === 'passed' ? 'success' :
                               status === 'failed' ? 'error' :
                               status === 'warning' ? 'warning' : 'testing';

            const statusIcon = status === 'passed' ? '✅' :
                              status === 'failed' ? '❌' :
                              status === 'warning' ? '⚠️' : '⏳';

            let html = `
                <div class="test-item ${statusClass}">
                    <div class="test-status">${statusIcon} ${test.name}</div>
                    <div class="test-description">${test.description}</div>
            `;

            if (result) {
                html += `<div class="test-details">${result.details || ''}</div>`;

                if (result.solution && status === 'failed') {
                    html += `
                        <div class="test-solution">
                            <strong>💡 الحل المقترح:</strong><br>
                            ${result.solution}
                        </div>
                    `;
                }
            }

            html += '</div>';
            return html;
        }
</body>
</html>
