<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Vela sweets</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- بداية الهيدر -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm">
            <div class="container">
                <a class="navbar-brand fw-bold fs-4" href="index.html">Vela sweets</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="index.html">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="products.html">المنتجات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="about.html">من نحن</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="contact.html">اتصل بنا</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <!-- نهاية الهيدر -->

    <!-- بداية عنوان الصفحة -->
    <div class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1>تسجيل الدخول</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="index.html" class="text-decoration-none">الرئيسية</a></li>
                            <li class="breadcrumb-item active" aria-current="page">تسجيل الدخول</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- نهاية عنوان الصفحة -->

    <!-- بداية نموذج تسجيل الدخول -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="card shadow-sm">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <h3 class="fw-bold">مرحباً بك مرة أخرى</h3>
                                <p class="text-muted">سجل دخولك للوصول إلى حسابك</p>
                            </div>

                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" required>
                                    <div class="invalid-feedback" id="emailError"></div>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback" id="passwordError"></div>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="rememberMe">
                                    <label class="form-check-label" for="rememberMe">
                                        تذكرني
                                    </label>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-dark btn-lg">تسجيل الدخول</button>
                                </div>

                                <div class="text-center mt-3">
                                    <a href="#" class="text-decoration-none">نسيت كلمة المرور؟</a>
                                </div>
                            </form>

                            <hr class="my-4">

                            <div class="text-center">
                                <p class="mb-0">ليس لديك حساب؟ <a href="#" class="text-decoration-none fw-bold">إنشاء حساب جديد</a></p>
                            </div>

                            <!-- رسائل النجاح والخطأ -->
                            <div id="loginMessage" class="mt-3" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- نهاية نموذج تسجيل الدخول -->

    <!-- بداية الفوتر -->
    <footer class="bg-dark text-white pt-5 pb-4">
        <div class="container">
            <div class="row">
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">Vela sweets</h5>
                    <p>متجر متخصص في الحلويات الفاخرة بجميع أنواعها، نقدم منتجات عالية الجودة بأسعار مناسبة.</p>
                </div>
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="index.html" class="text-decoration-none text-white">الرئيسية</a></li>
                        <li class="mb-2"><a href="products.html" class="text-decoration-none text-white">المنتجات</a></li>
                        <li class="mb-2"><a href="about.html" class="text-decoration-none text-white">من نحن</a></li>
                        <li class="mb-2"><a href="contact.html" class="text-decoration-none text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">سياسات المتجر</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="privacy.html" class="text-decoration-none text-white">سياسة الخصوصية</a></li>
                        <li class="mb-2"><a href="terms.html" class="text-decoration-none text-white">الشروط والأحكام</a></li>
                        <li class="mb-2"><a href="shipping.html" class="text-decoration-none text-white">سياسة الشحن</a></li>
                        <li class="mb-2"><a href="return.html" class="text-decoration-none text-white">سياسة الاسترجاع</a></li>
                        <li class="mb-2"><a href="faq.html" class="text-decoration-none text-white">الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5 class="mb-4">تواصل معنا</h5>
                    <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i> البصرة، العراق</p>
                    <p class="mb-2"><i class="fas fa-phone me-2"></i> 07xxxxxxxxx</p>
                    <p class="mb-2"><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <div class="mt-4">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">جميع الحقوق محفوظة © 2025 Velasweets</p>
                </div>
            </div>
        </div>
    </footer>
    <!-- نهاية الفوتر -->

    <!-- سكريبت بوتستراب -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- سكريبت مخصص -->
    <script src="js/main.js"></script>
    
    <script>
        // وظائف تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            const loginMessage = document.getElementById('loginMessage');

            // إظهار/إخفاء كلمة المرور
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });

            // معالجة نموذج تسجيل الدخول
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                
                // تنظيف الأخطاء السابقة
                clearErrors();
                
                // التحقق من صحة البيانات
                if (!validateLogin(email, password)) {
                    return;
                }
                
                // محاكاة عملية تسجيل الدخول
                simulateLogin(email, password);
            });

            function validateLogin(email, password) {
                let isValid = true;
                
                // التحقق من البريد الإلكتروني
                if (!email || !isValidEmail(email)) {
                    showError('emailError', 'يرجى إدخال بريد إلكتروني صحيح');
                    isValid = false;
                }
                
                // التحقق من كلمة المرور
                if (!password || password.length < 6) {
                    showError('passwordError', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                    isValid = false;
                }
                
                return isValid;
            }

            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            function showError(elementId, message) {
                const errorElement = document.getElementById(elementId);
                errorElement.textContent = message;
                errorElement.parentElement.querySelector('input').classList.add('is-invalid');
            }

            function clearErrors() {
                const errorElements = document.querySelectorAll('.invalid-feedback');
                const inputElements = document.querySelectorAll('.form-control');
                
                errorElements.forEach(el => el.textContent = '');
                inputElements.forEach(el => el.classList.remove('is-invalid', 'is-valid'));
            }

            function simulateLogin(email, password) {
                // إظهار رسالة التحميل
                showMessage('جاري تسجيل الدخول...', 'info');
                
                // محاكاة تأخير الشبكة
                setTimeout(() => {
                    // بيانات تجريبية للتسجيل
                    const validCredentials = {
                        '<EMAIL>': 'admin123',
                        '<EMAIL>': 'user123'
                    };
                    
                    if (validCredentials[email] && validCredentials[email] === password) {
                        // نجح تسجيل الدخول
                        showMessage('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                        
                        // حفظ بيانات المستخدم
                        localStorage.setItem('userLoggedIn', 'true');
                        localStorage.setItem('userEmail', email);
                        
                        // التوجيه إلى لوحة المستخدم بعد ثانيتين
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 2000);
                    } else {
                        // فشل تسجيل الدخول
                        showMessage('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
                    }
                }, 1500);
            }

            function showMessage(message, type) {
                const messageElement = document.getElementById('loginMessage');
                messageElement.style.display = 'block';
                messageElement.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
                messageElement.textContent = message;
            }
        });
    </script>
</body>
</html>
